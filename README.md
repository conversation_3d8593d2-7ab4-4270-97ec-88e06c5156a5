# Learning Coach Community
React Native/Expo app for the Learning Coach Community

## Directories
- `assets` (@assets)
  - `images`
  - `fonts`
- `src` (@/) - The main app directory
  - `api/` (@/api): Contains API calls and network-related code.
  - `components/` (@/components): Reusable UI components.
  - `config/` (@/config): Configuration files, constants, and environment variables.
  - `hooks/` (@/hooks): Custom hooks.
  - `navigation/` (@/navigation): Navigation setup and configurations.
  - `repositories/` (@/repositories): Data access layers, interfaces for APIs, databases, etc.
  - `screens/` (@/screens): Screen components.
  - `services/` (@/services): Business logic, interacting with repositories.
  - `styles/` (@/styles): Styling and theming.
  - `usecases/` (@/usecases): Specific application use cases or business rules.
  - `utils/` (@/utils): Utility functions.

## Architecture

This project follows **Clean Architecture** principles to ensure maintainability, testability, and separation of concerns. Understanding these patterns is crucial for consistent development across the team.

### Core Principles

1. **Dependency Inversion**: Higher-level modules should not depend on lower-level modules. Both should depend on abstractions.
2. **Single Responsibility**: Each class/module should have one reason to change.
3. **Separation of Concerns**: Different aspects of the application should be handled by different layers.
4. **Testability**: Each layer should be independently testable through dependency injection and mocking.

### Layer Definitions

#### **Services Layer** (`/src/services/`)
- **Purpose**: Business logic and application workflows
- **Responsibilities**:
  - Domain-specific business rules and validation
  - Complex business operations and orchestration
  - Application-specific logic and workflows
  - Business rule enforcement and data transformation
  - Coordinating between multiple repositories
- **Dependencies**: Repositories layer
- **Examples**: `authService.ts`, `userService.ts`, `apiConfigService.ts`

#### **Repositories Layer** (`/src/repositories/`)
- **Purpose**: Data access and storage abstraction
- **Responsibilities**:
  - Data access interfaces and CRUD operations
  - Storage mechanism abstraction (APIs, databases, local storage)
  - Platform-specific implementations (iOS/Android/Web differences)
  - Third-party library integrations (Expo SecureStore, AsyncStorage, etc.)
  - Low-level data operations and persistence
- **Dependencies**: External libraries, platform APIs
- **Examples**: `secureStorageRepository.ts`, `userRepository.ts`, `apiRepository.ts`

#### **Use Cases Layer** (`/src/usecases/`)
- **Purpose**: Application-specific business rules and workflows
- **Responsibilities**:
  - Orchestrate complex multi-service workflows
  - Coordinate between multiple services
  - Implement application-specific validation rules
  - Handle business logic that spans multiple domains
- **Dependencies**: Services layer
- **Examples**: `authenticateUser.ts`, `syncUserData.ts`, `processPayment.ts`

#### **API Layer** (`/src/api/`)
- **Purpose**: External communication interfaces
- **Responsibilities**:
  - HTTP request/response handling
  - API endpoint definitions
  - Request/response transformation
  - Network error handling
- **Dependencies**: Repositories layer (for data access)
- **Examples**: `authApi.ts`, `userApi.ts`, `contentApi.ts`

#### **Components Layer** (`/src/components/`)
- **Purpose**: Reusable UI elements
- **Responsibilities**:
  - Pure UI logic
  - Component-specific state management
  - Props validation and transformation
  - Accessibility implementations
- **Dependencies**: Hooks, Utils
- **Examples**: `Button.tsx`, `Modal.tsx`, `UserCard.tsx`

#### **Screens Layer** (`/src/screens/`)
- **Purpose**: Application screens and navigation targets
- **Responsibilities**:
  - Screen-level state management
  - Coordinate UI components
  - Handle navigation logic
  - Connect to use cases and services
- **Dependencies**: Components, Hooks, Use Cases, Services
- **Examples**: `LoginScreen.tsx`, `ProfileScreen.tsx`, `HomeScreen.tsx`

#### **Hooks Layer** (`/src/hooks/`)
- **Purpose**: Reusable stateful logic
- **Responsibilities**:
  - Custom React hooks for shared logic
  - State management patterns
  - Side effect management
  - Component lifecycle abstractions
- **Dependencies**: Use Cases, Services, Repositories
- **Examples**: `useAuth.ts`, `useSecureStorage.ts`, `useApi.ts`

### Dependency Flow

```
Screens → Hooks → Use Cases → Services → Repositories → External APIs/Storage
   ↓         ↓         ↓           ↓            ↓
Components   Utils   Config    Domain Models  Platform APIs
```

### Best Practices

#### **Naming Conventions**
- **Services**: `[domain]Service.ts` (e.g., `authService.ts`, `userService.ts`)
- **Repositories**: `[domain]Repository.ts` (e.g., `secureStorageRepository.ts`, `apiRepository.ts`)
- **Use Cases**: `[action][Domain].ts` (e.g., `authenticateUser.ts`, `fetchUserProfile.ts`)
- **APIs**: `[domain]Api.ts` (e.g., `authApi.ts`)
- **Hooks**: `use[Domain].ts` (e.g., `useAuth.ts`)

#### **Error Handling**
- Repositories should throw technical/infrastructure errors
- Services should handle business logic errors and validation
- Use Cases should coordinate error handling across domains
- Components should handle UI-specific error states

#### **Testing Strategy**
- **Unit Tests**: Test each layer in isolation using mocks
- **Integration Tests**: Test layer interactions
- **E2E Tests**: Test complete user workflows

#### **Import Rules**
- Never import from a higher layer (e.g., Repositories should not import from Services)
- Use dependency injection for testability
- Prefer interfaces over concrete implementations for dependencies

### Example: Authentication Flow

```typescript
// 1. Screen calls hook
const { login } = useAuth();

// 2. Hook calls use case
const loginResult = await authenticateUser(credentials);

// 3. Use case coordinates services
const authData = await authService.validateCredentials(credentials);
await userService.saveUserSession(authData);

// 4. Service uses repositories
await secureStorageRepository.setItem('auth_token', authData.token);
```

This architecture ensures that business logic is separated from technical implementation, making the codebase more maintainable, testable, and adaptable to changing requirements.

## Get started

1. Install dependencies

   ```bash
   npm install
   ```

2. Start the app

   ```bash
   npx expo start
   ```

In the output, you'll find options to open the app in a

- [development build](https://docs.expo.dev/develop/development-builds/introduction/)
- [Android emulator](https://docs.expo.dev/workflow/android-studio-emulator/)
- [iOS simulator](https://docs.expo.dev/workflow/ios-simulator/)
- [Expo Go](https://expo.dev/go), a limited sandbox for trying out app development with Expo

You can start developing by editing the files inside the **app** directory. This project uses [file-based routing](https://docs.expo.dev/router/introduction).


## Learn more

To learn more about developing your project with Expo, look at the following resources:

- [Expo documentation](https://docs.expo.dev/): Learn fundamentals, or go into advanced topics with our [guides](https://docs.expo.dev/guides).
- [Learn Expo tutorial](https://docs.expo.dev/tutorial/introduction/): Follow a step-by-step tutorial where you'll create a project that runs on Android, iOS, and the web.

## Join the community

Join our community of developers creating universal apps.

- [Expo on GitHub](https://github.com/expo/expo): View our open source platform and contribute.
- [Discord community](https://chat.expo.dev): Chat with Expo users and ask questions.
