/**
 * Secure Storage Service
 *
 * Business logic layer for secure storage operations.
 * Handles domain-specific operations, validation, and complex workflows.
 */

import { secureStorageRepository } from '../repositories/secureStorageRepository';

/**
 * User authentication data interface
 */
export interface AuthData {
	token: string;
	refreshToken: string;
	expiresAt: number;
}

/**
 * User preferences interface
 */
export interface UserPreferences {
	theme: 'light' | 'dark' | 'system';
	language: string;
	notifications: boolean;
	biometricEnabled: boolean;
}

/**
 * Service for secure storage business operations
 */
export class SecureStorageService {
	/**
	 * Store authentication data with validation
	 */
	async saveAuthData(authData: AuthData): Promise<void> {
		// Business validation
		if (!authData.token || !authData.refreshToken) {
			throw new Error('Authentication data must include both token and refresh token');
		}

		if (authData.expiresAt <= Date.now()) {
			throw new Error('Cannot save expired authentication data');
		}

		try {
			await secureStorageRepository.saveAuthData(authData);
		} catch (error) {
			throw new Error(`Failed to save authentication data: ${error}`);
		}
	}

	/**
	 * Retrieve authentication data
	 */
	async getAuthData(): Promise<AuthData | null> {
		try {
			return await secureStorageRepository.getAuthData();
		} catch (error) {
			console.error('Failed to retrieve authentication data:', error);
			return null;
		}
	}

	/**
	 * Clear authentication data
	 */
	async clearAuthData(): Promise<void> {
		try {
			await secureStorageRepository.clearAuthData();
		} catch (error) {
			throw new Error(`Failed to clear authentication data: ${error}`);
		}
	}

	/**
	 * Check if user is authenticated (business logic)
	 */
	async isAuthenticated(): Promise<boolean> {
		try {
			const authData = await this.getAuthData();
			if (!authData) {
				return false;
			}

			// Business logic: Check if token is expired
			const now = Date.now();
			return authData.expiresAt > now;
		} catch (error) {
			console.error('Failed to check authentication status:', error);
			return false;
		}
	}

	/**
	 * Refresh authentication if needed (business logic)
	 */
	async refreshAuthIfNeeded(): Promise<boolean> {
		try {
			const authData = await this.getAuthData();
			if (!authData) {
				return false;
			}

			// Business logic: Check if token expires within 5 minutes
			const fiveMinutesFromNow = Date.now() + (5 * 60 * 1000);
			const needsRefresh = authData.expiresAt <= fiveMinutesFromNow;

			if (needsRefresh) {
				// In a real app, this would call an API to refresh the token
				console.log('Token needs refresh - implement refresh logic here');
				return false;
			}

			return true;
		} catch (error) {
			console.error('Failed to check if auth refresh is needed:', error);
			return false;
		}
	}

	/**
	 * Save user preferences with validation
	 */
	async saveUserPreferences(preferences: UserPreferences): Promise<void> {
		// Business validation
		if (!['light', 'dark', 'system'].includes(preferences.theme)) {
			throw new Error('Invalid theme preference');
		}

		if (!preferences.language || preferences.language.length < 2) {
			throw new Error('Invalid language preference');
		}

		try {
			await secureStorageRepository.saveUserPreferences(preferences);
		} catch (error) {
			throw new Error(`Failed to save user preferences: ${error}`);
		}
	}

	/**
	 * Get user preferences with defaults
	 */
	async getUserPreferences(): Promise<UserPreferences> {
		try {
			const preferences = await secureStorageRepository.getUserPreferences();
			
			// Business logic: Provide defaults if no preferences exist
			if (!preferences) {
				return {
					theme: 'system',
					language: 'en',
					notifications: true,
					biometricEnabled: false,
				};
			}

			return preferences;
		} catch (error) {
			console.error('Failed to retrieve user preferences:', error);
			// Return defaults on error
			return {
				theme: 'system',
				language: 'en',
				notifications: true,
				biometricEnabled: false,
			};
		}
	}

	/**
	 * Update biometric setting with validation
	 */
	async setBiometricEnabled(enabled: boolean): Promise<void> {
		try {
			await secureStorageRepository.setBiometricEnabled(enabled);
			
			// Business logic: Update user preferences to keep them in sync
			const preferences = await this.getUserPreferences();
			preferences.biometricEnabled = enabled;
			await this.saveUserPreferences(preferences);
		} catch (error) {
			throw new Error(`Failed to update biometric setting: ${error}`);
		}
	}

	/**
	 * Check if biometric is enabled
	 */
	async isBiometricEnabled(): Promise<boolean> {
		try {
			return await secureStorageRepository.isBiometricEnabled();
		} catch (error) {
			console.error('Failed to check biometric setting:', error);
			return false;
		}
	}

	/**
	 * Save last login timestamp (business logic)
	 */
	async recordLogin(): Promise<void> {
		try {
			await secureStorageRepository.saveLastLogin();
		} catch (error) {
			throw new Error(`Failed to record login: ${error}`);
		}
	}

	/**
	 * Get last login with business logic
	 */
	async getLastLoginInfo(): Promise<{ date: Date | null; daysAgo: number | null }> {
		try {
			const lastLogin = await secureStorageRepository.getLastLogin();
			
			if (!lastLogin) {
				return { date: null, daysAgo: null };
			}

			// Business logic: Calculate days since last login
			const now = new Date();
			const diffTime = Math.abs(now.getTime() - lastLogin.getTime());
			const daysAgo = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

			return { date: lastLogin, daysAgo };
		} catch (error) {
			console.error('Failed to retrieve last login info:', error);
			return { date: null, daysAgo: null };
		}
	}

	/**
	 * Store generic secure data with validation
	 */
	async storeSecureData<T>(key: string, data: T): Promise<void> {
		// Business validation
		if (!key || key.trim().length === 0) {
			throw new Error('Storage key cannot be empty');
		}

		if (data === null || data === undefined) {
			throw new Error('Cannot store null or undefined data');
		}

		try {
			await secureStorageRepository.storeSecureData(key, data);
		} catch (error) {
			throw new Error(`Failed to store secure data for key ${key}: ${error}`);
		}
	}

	/**
	 * Retrieve generic secure data
	 */
	async getSecureData<T>(key: string): Promise<T | null> {
		if (!key || key.trim().length === 0) {
			throw new Error('Storage key cannot be empty');
		}

		try {
			return await secureStorageRepository.getSecureData<T>(key);
		} catch (error) {
			console.error(`Failed to retrieve secure data for key ${key}:`, error);
			return null;
		}
	}

	/**
	 * Remove specific secure data
	 */
	async removeSecureData(key: string): Promise<void> {
		if (!key || key.trim().length === 0) {
			throw new Error('Storage key cannot be empty');
		}

		try {
			await secureStorageRepository.removeSecureData(key);
		} catch (error) {
			throw new Error(`Failed to remove secure data for key ${key}: ${error}`);
		}
	}

	/**
	 * Perform complete logout (business workflow)
	 */
	async logout(): Promise<void> {
		try {
			await secureStorageRepository.clearAllUserData();
		} catch (error) {
			throw new Error(`Failed to logout: ${error}`);
		}
	}

	/**
	 * Check if user has any stored data (business logic)
	 */
	async hasUserData(): Promise<boolean> {
		try {
			const authData = await this.getAuthData();
			return authData !== null;
		} catch (error) {
			console.error('Failed to check if user has data:', error);
			return false;
		}
	}
}

// Export singleton instance
export const secureStorageService = new SecureStorageService();
