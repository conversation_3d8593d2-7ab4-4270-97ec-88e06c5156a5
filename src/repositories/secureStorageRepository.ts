/**
 * Secure Storage Repository
 *
 * Data access layer for secure storage operations.
 * Provides low-level storage operations and platform abstraction.
 */

import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';

/**
 * Storage options for SecureStore
 */
interface StorageOptions {
	requireAuthentication?: boolean;
	authenticationPrompt?: string;
}

/**
 * User authentication data interface
 */
export interface AuthData {
	token: string;
	refreshToken: string;
	expiresAt: number;
}

/**
 * User preferences interface
 */
export interface UserPreferences {
	theme: 'light' | 'dark' | 'system';
	language: string;
	notifications: boolean;
	biometricEnabled: boolean;
}

/**
 * Storage keys constants
 */
export const StorageKeys = {
	AUTH_TOKEN: 'auth_token',
	REFRESH_TOKEN: 'refresh_token',
	USER_PREFERENCES: 'user_preferences',
	BIOMETRIC_ENABLED: 'biometric_enabled',
	LAST_LOGIN: 'last_login',
} as const;

export type StorageKey = (typeof StorageKeys)[keyof typeof StorageKeys];

/**
 * Repository for secure storage data access operations
 */
export class SecureStorageRepository {
	private isSecureStoreAvailable: boolean;

	constructor() {
		// SecureStore is available on iOS and Android, but not on web
		this.isSecureStoreAvailable = Platform.OS !== 'web';
	}

	/**
	 * Store a value securely
	 */
	async setItem(key: string, value: string, options?: StorageOptions): Promise<void> {
		try {
			if (this.isSecureStoreAvailable) {
				await SecureStore.setItemAsync(key, value, options);
			} else {
				throw new Error('Secure storage is not available on this platform');
			}
		} catch (error) {
			console.error(`Failed to store item with key ${key}:`, error);
			throw new Error(`Storage operation failed: ${error}`);
		}
	}

	/**
	 * Retrieve a value securely
	 */
	async getItem(key: string, options?: StorageOptions): Promise<string | null> {
		try {
			if (this.isSecureStoreAvailable) {
				return await SecureStore.getItemAsync(key, options);
			} else {
				console.warn(
					'Secure storage is not available on this platform; getItem returning null',
				);
				return null;
			}
		} catch (error) {
			console.error(`Failed to retrieve item with key ${key}:`, error);
			return null;
		}
	}

	/**
	 * Remove a value securely
	 */
	async removeItem(key: string): Promise<void> {
		try {
			if (this.isSecureStoreAvailable) {
				await SecureStore.deleteItemAsync(key);
			} else {
				throw new Error('Secure storage is not available on this platform');
			}
		} catch (error) {
			console.error(`Failed to remove item with key ${key}:`, error);
			throw new Error(`Storage operation failed: ${error}`);
		}
	}

	/**
	 * Check if a key exists
	 */
	async hasItem(key: string): Promise<boolean> {
		try {
			const value = await this.getItem(key);
			return value !== null;
		} catch (error) {
			console.error(`Failed to check item with key ${key}:`, error);
			return false;
		}
	}

	/**
	 * Store JSON data securely
	 */
	async setObject<T>(key: string, value: T, options?: StorageOptions): Promise<void> {
		try {
			const jsonString = JSON.stringify(value);
			await this.setItem(key, jsonString, options);
		} catch (error) {
			console.error(`Failed to store object with key ${key}:`, error);
			throw new Error(`Object storage operation failed: ${error}`);
		}
	}

	/**
	 * Retrieve JSON data securely
	 */
	async getObject<T>(key: string, options?: StorageOptions): Promise<T | null> {
		try {
			const jsonString = await this.getItem(key, options);
			if (jsonString === null) {
				return null;
			}
			return JSON.parse(jsonString) as T;
		} catch (error) {
			console.error(`Failed to retrieve object with key ${key}:`, error);
			return null;
		}
	}

	/**
	 * Store authentication data (data access only)
	 */
	async saveAuthData(authData: AuthData): Promise<void> {
		try {
			await Promise.all([
				this.setItem(StorageKeys.AUTH_TOKEN, authData.token),
				this.setItem(StorageKeys.REFRESH_TOKEN, authData.refreshToken),
				this.setObject('auth_expires_at', { expiresAt: authData.expiresAt }),
			]);
		} catch (error) {
			throw new Error(`Failed to save authentication data: ${error}`);
		}
	}

	/**
	 * Retrieve authentication data (data access only)
	 */
	async getAuthData(): Promise<AuthData | null> {
		try {
			const [token, refreshToken, expiresData] = await Promise.all([
				this.getItem(StorageKeys.AUTH_TOKEN),
				this.getItem(StorageKeys.REFRESH_TOKEN),
				this.getObject<{ expiresAt: number }>('auth_expires_at'),
			]);

			if (!token || !refreshToken || !expiresData) {
				return null;
			}

			return {
				token,
				refreshToken,
				expiresAt: expiresData.expiresAt,
			};
		} catch (error) {
			console.error('Failed to retrieve authentication data:', error);
			return null;
		}
	}

	/**
	 * Clear authentication data (data access only)
	 */
	async clearAuthData(): Promise<void> {
		try {
			await Promise.all([
				this.removeItem(StorageKeys.AUTH_TOKEN),
				this.removeItem(StorageKeys.REFRESH_TOKEN),
				this.removeItem('auth_expires_at'),
			]);
		} catch (error) {
			throw new Error(`Failed to clear authentication data: ${error}`);
		}
	}

	/**
	 * Save user preferences (data access only)
	 */
	async saveUserPreferences(preferences: UserPreferences): Promise<void> {
		try {
			await this.setObject(StorageKeys.USER_PREFERENCES, preferences);
		} catch (error) {
			throw new Error(`Failed to save user preferences: ${error}`);
		}
	}

	/**
	 * Get user preferences (data access only)
	 */
	async getUserPreferences(): Promise<UserPreferences | null> {
		try {
			return await this.getObject<UserPreferences>(StorageKeys.USER_PREFERENCES);
		} catch (error) {
			console.error('Failed to retrieve user preferences:', error);
			return null;
		}
	}

	/**
	 * Update biometric setting (data access only)
	 */
	async setBiometricEnabled(enabled: boolean): Promise<void> {
		try {
			await this.setItem(StorageKeys.BIOMETRIC_ENABLED, enabled.toString());
		} catch (error) {
			throw new Error(`Failed to update biometric setting: ${error}`);
		}
	}

	/**
	 * Check if biometric is enabled (data access only)
	 */
	async isBiometricEnabled(): Promise<boolean> {
		try {
			const value = await this.getItem(StorageKeys.BIOMETRIC_ENABLED);
			return value === 'true';
		} catch (error) {
			console.error('Failed to check biometric setting:', error);
			return false;
		}
	}

	/**
	 * Save last login timestamp (data access only)
	 */
	async saveLastLogin(): Promise<void> {
		try {
			const timestamp = Date.now().toString();
			await this.setItem(StorageKeys.LAST_LOGIN, timestamp);
		} catch (error) {
			throw new Error(`Failed to save last login: ${error}`);
		}
	}

	/**
	 * Get last login timestamp (data access only)
	 */
	async getLastLogin(): Promise<Date | null> {
		try {
			const timestamp = await this.getItem(StorageKeys.LAST_LOGIN);
			if (!timestamp) {
				return null;
			}
			return new Date(parseInt(timestamp, 10));
		} catch (error) {
			console.error('Failed to retrieve last login:', error);
			return null;
		}
	}

	/**
	 * Store generic secure data (data access only)
	 */
	async storeSecureData<T>(key: string, data: T): Promise<void> {
		try {
			await this.setObject(key, data);
		} catch (error) {
			throw new Error(`Failed to store secure data for key ${key}: ${error}`);
		}
	}

	/**
	 * Retrieve generic secure data (data access only)
	 */
	async getSecureData<T>(key: string): Promise<T | null> {
		try {
			return await this.getObject<T>(key);
		} catch (error) {
			console.error(`Failed to retrieve secure data for key ${key}:`, error);
			return null;
		}
	}

	/**
	 * Remove specific secure data (data access only)
	 */
	async removeSecureData(key: string): Promise<void> {
		try {
			await this.removeItem(key);
		} catch (error) {
			throw new Error(`Failed to remove secure data for key ${key}: ${error}`);
		}
	}

	/**
	 * Clear all user data (data access only)
	 */
	async clearAllUserData(): Promise<void> {
		try {
			await Promise.all([
				this.clearAuthData(),
				this.removeItem(StorageKeys.USER_PREFERENCES),
				this.removeItem(StorageKeys.BIOMETRIC_ENABLED),
				this.removeItem(StorageKeys.LAST_LOGIN),
			]);
		} catch (error) {
			throw new Error(`Failed to clear all user data: ${error}`);
		}
	}
}

// Export singleton instance
export const secureStorageRepository = new SecureStorageRepository();
