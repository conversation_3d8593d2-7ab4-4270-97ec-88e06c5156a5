/**
 * Tests for SecureStorageRepository
 */

import { SecureStorageRepository, StorageKeys } from './secureStorageRepository';

// Mock the dependencies
jest.mock('expo-secure-store', () => ({
	setItemAsync: jest.fn(),
	getItemAsync: jest.fn(),
	deleteItemAsync: jest.fn(),
}));

jest.mock('react-native', () => ({
	Platform: {
		OS: 'ios', // Default to iOS for SecureStore availability
	},
}));

describe('SecureStorageRepository', () => {
	let repository;

	beforeEach(() => {
		repository = new SecureStorageRepository();
		jest.clearAllMocks();
		jest.spyOn(console, 'error').mockImplementation(() => {});
	});

	afterEach(() => {
		jest.restoreAllMocks();
	});

	describe('Authentication Data', () => {
		const mockAuthData = {
			token: 'mock-token',
			refreshToken: 'mock-refresh-token',
			expiresAt: Date.now() + 3600000, // 1 hour from now
		};

		describe('saveAuthData', () => {
			it('should save authentication data successfully', async () => {
				secureStorage.setItem.mockResolvedValue();
				secureStorage.setObject.mockResolvedValue();

				await repository.saveAuthData(mockAuthData);

				expect(secureStorage.setItem).toHaveBeenCalledWith(
					StorageKeys.AUTH_TOKEN,
					mockAuthData.token,
				);
				expect(secureStorage.setItem).toHaveBeenCalledWith(
					StorageKeys.REFRESH_TOKEN,
					mockAuthData.refreshToken,
				);
				expect(secureStorage.setObject).toHaveBeenCalledWith('auth_expires_at', {
					expiresAt: mockAuthData.expiresAt,
				});
			});

			it('should throw error when storage fails', async () => {
				const error = new Error('Storage failed');
				secureStorage.setItem.mockRejectedValue(error);

				await expect(repository.saveAuthData(mockAuthData)).rejects.toThrow(
					'Failed to save authentication data: Error: Storage failed',
				);
			});
		});

		describe('getAuthData', () => {
			it('should retrieve authentication data successfully', async () => {
				secureStorage.getItem
					.mockResolvedValueOnce(mockAuthData.token)
					.mockResolvedValueOnce(mockAuthData.refreshToken);
				secureStorage.getObject.mockResolvedValue({ expiresAt: mockAuthData.expiresAt });

				const result = await repository.getAuthData();

				expect(result).toEqual(mockAuthData);
				expect(secureStorage.getItem).toHaveBeenCalledWith(StorageKeys.AUTH_TOKEN);
				expect(secureStorage.getItem).toHaveBeenCalledWith(StorageKeys.REFRESH_TOKEN);
				expect(secureStorage.getObject).toHaveBeenCalledWith('auth_expires_at');
			});

			it('should return null when data is incomplete', async () => {
				secureStorage.getItem
					.mockResolvedValueOnce(mockAuthData.token)
					.mockResolvedValueOnce(null); // missing refresh token
				secureStorage.getObject.mockResolvedValue({ expiresAt: mockAuthData.expiresAt });

				const result = await repository.getAuthData();

				expect(result).toBeNull();
			});

			it('should return null when storage throws error', async () => {
				secureStorage.getItem.mockRejectedValue(new Error('Storage error'));

				const result = await repository.getAuthData();

				expect(result).toBeNull();
			});
		});

		describe('clearAuthData', () => {
			it('should clear all authentication data', async () => {
				secureStorage.removeItem.mockResolvedValue();

				await repository.clearAuthData();

				expect(secureStorage.removeItem).toHaveBeenCalledWith(StorageKeys.AUTH_TOKEN);
				expect(secureStorage.removeItem).toHaveBeenCalledWith(StorageKeys.REFRESH_TOKEN);
				expect(secureStorage.removeItem).toHaveBeenCalledWith('auth_expires_at');
			});

			it('should throw error when removal fails', async () => {
				secureStorage.removeItem.mockRejectedValue(new Error('Removal failed'));

				await expect(repository.clearAuthData()).rejects.toThrow(
					'Failed to clear authentication data: Error: Removal failed',
				);
			});
		});

		describe('isAuthenticated', () => {
			it('should return true for valid non-expired token', async () => {
				const futureTime = Date.now() + 3600000; // 1 hour from now
				secureStorage.getItem
					.mockResolvedValueOnce('token')
					.mockResolvedValueOnce('refresh-token');
				secureStorage.getObject.mockResolvedValue({ expiresAt: futureTime });

				const result = await repository.isAuthenticated();

				expect(result).toBe(true);
			});

			it('should return false for expired token', async () => {
				const pastTime = Date.now() - 3600000; // 1 hour ago
				secureStorage.getItem
					.mockResolvedValueOnce('token')
					.mockResolvedValueOnce('refresh-token');
				secureStorage.getObject.mockResolvedValue({ expiresAt: pastTime });

				const result = await repository.isAuthenticated();

				expect(result).toBe(false);
			});

			it('should return false when no auth data exists', async () => {
				secureStorage.getItem.mockResolvedValue(null);
				secureStorage.getObject.mockResolvedValue(null);

				const result = await repository.isAuthenticated();

				expect(result).toBe(false);
			});

			it('should return false when error occurs', async () => {
				secureStorage.getItem.mockRejectedValue(new Error('Storage error'));

				const result = await repository.isAuthenticated();

				expect(result).toBe(false);
			});
		});
	});

	describe('User Preferences', () => {
		const mockPreferences = {
			theme: 'dark',
			language: 'en',
			notifications: true,
			biometricEnabled: false,
		};

		describe('saveUserPreferences', () => {
			it('should save user preferences successfully', async () => {
				secureStorage.setObject.mockResolvedValue();

				await repository.saveUserPreferences(mockPreferences);

				expect(secureStorage.setObject).toHaveBeenCalledWith(
					StorageKeys.USER_PREFERENCES,
					mockPreferences,
				);
			});

			it('should throw error when save fails', async () => {
				secureStorage.setObject.mockRejectedValue(new Error('Save failed'));

				await expect(repository.saveUserPreferences(mockPreferences)).rejects.toThrow(
					'Failed to save user preferences: Error: Save failed',
				);
			});
		});

		describe('getUserPreferences', () => {
			it('should retrieve user preferences successfully', async () => {
				secureStorage.getObject.mockResolvedValue(mockPreferences);

				const result = await repository.getUserPreferences();

				expect(result).toEqual(mockPreferences);
				expect(secureStorage.getObject).toHaveBeenCalledWith(StorageKeys.USER_PREFERENCES);
			});

			it('should return null when no preferences exist', async () => {
				secureStorage.getObject.mockResolvedValue(null);

				const result = await repository.getUserPreferences();

				expect(result).toBeNull();
			});

			it('should return null when error occurs', async () => {
				secureStorage.getObject.mockRejectedValue(new Error('Storage error'));

				const result = await repository.getUserPreferences();

				expect(result).toBeNull();
			});
		});
	});

	describe('Biometric Settings', () => {
		describe('setBiometricEnabled', () => {
			it('should save biometric enabled setting', async () => {
				secureStorage.setItem.mockResolvedValue();

				await repository.setBiometricEnabled(true);

				expect(secureStorage.setItem).toHaveBeenCalledWith(
					StorageKeys.BIOMETRIC_ENABLED,
					'true',
				);
			});

			it('should save biometric disabled setting', async () => {
				secureStorage.setItem.mockResolvedValue();

				await repository.setBiometricEnabled(false);

				expect(secureStorage.setItem).toHaveBeenCalledWith(
					StorageKeys.BIOMETRIC_ENABLED,
					'false',
				);
			});

			it('should throw error when save fails', async () => {
				secureStorage.setItem.mockRejectedValue(new Error('Save failed'));

				await expect(repository.setBiometricEnabled(true)).rejects.toThrow(
					'Failed to update biometric setting: Error: Save failed',
				);
			});
		});

		describe('isBiometricEnabled', () => {
			it('should return true when biometric is enabled', async () => {
				secureStorage.getItem.mockResolvedValue('true');

				const result = await repository.isBiometricEnabled();

				expect(result).toBe(true);
			});

			it('should return false when biometric is disabled', async () => {
				secureStorage.getItem.mockResolvedValue('false');

				const result = await repository.isBiometricEnabled();

				expect(result).toBe(false);
			});

			it('should return false when no setting exists', async () => {
				secureStorage.getItem.mockResolvedValue(null);

				const result = await repository.isBiometricEnabled();

				expect(result).toBe(false);
			});

			it('should return false when error occurs', async () => {
				secureStorage.getItem.mockRejectedValue(new Error('Storage error'));

				const result = await repository.isBiometricEnabled();

				expect(result).toBe(false);
			});
		});
	});

	describe('Last Login', () => {
		describe('saveLastLogin', () => {
			it('should save current timestamp as last login', async () => {
				secureStorage.setItem.mockResolvedValue();
				const mockDate = new Date('2023-01-01T12:00:00Z');
				jest.spyOn(Date, 'now').mockReturnValue(mockDate.getTime());

				await repository.saveLastLogin();

				expect(secureStorage.setItem).toHaveBeenCalledWith(
					StorageKeys.LAST_LOGIN,
					mockDate.getTime().toString(),
				);
			});

			it('should throw error when save fails', async () => {
				secureStorage.setItem.mockRejectedValue(new Error('Save failed'));

				await expect(repository.saveLastLogin()).rejects.toThrow(
					'Failed to save last login: Error: Save failed',
				);
			});
		});

		describe('getLastLogin', () => {
			it('should retrieve last login as Date object', async () => {
				const mockTimestamp = '1672574400000'; // 2023-01-01T12:00:00Z
				secureStorage.getItem.mockResolvedValue(mockTimestamp);

				const result = await repository.getLastLogin();

				expect(result).toEqual(new Date(parseInt(mockTimestamp, 10)));
				expect(secureStorage.getItem).toHaveBeenCalledWith(StorageKeys.LAST_LOGIN);
			});

			it('should return null when no last login exists', async () => {
				secureStorage.getItem.mockResolvedValue(null);

				const result = await repository.getLastLogin();

				expect(result).toBeNull();
			});

			it('should return null when error occurs', async () => {
				secureStorage.getItem.mockRejectedValue(new Error('Storage error'));

				const result = await repository.getLastLogin();

				expect(result).toBeNull();
			});
		});
	});

	describe('Generic Secure Data', () => {
		const testData = { id: 1, name: 'Test Data', active: true };

		describe('storeSecureData', () => {
			it('should store generic data successfully', async () => {
				secureStorage.setObject.mockResolvedValue();

				await repository.storeSecureData('custom-key', testData);

				expect(secureStorage.setObject).toHaveBeenCalledWith('custom-key', testData);
			});

			it('should throw error when storage fails', async () => {
				secureStorage.setObject.mockRejectedValue(new Error('Storage failed'));

				await expect(repository.storeSecureData('custom-key', testData)).rejects.toThrow(
					'Failed to store secure data for key custom-key: Error: Storage failed',
				);
			});
		});

		describe('getSecureData', () => {
			it('should retrieve generic data successfully', async () => {
				secureStorage.getObject.mockResolvedValue(testData);

				const result = await repository.getSecureData('custom-key');

				expect(result).toEqual(testData);
				expect(secureStorage.getObject).toHaveBeenCalledWith('custom-key');
			});

			it('should return null when no data exists', async () => {
				secureStorage.getObject.mockResolvedValue(null);

				const result = await repository.getSecureData('custom-key');

				expect(result).toBeNull();
			});

			it('should return null when error occurs', async () => {
				secureStorage.getObject.mockRejectedValue(new Error('Storage error'));

				const result = await repository.getSecureData('custom-key');

				expect(result).toBeNull();
			});
		});

		describe('removeSecureData', () => {
			it('should remove data successfully', async () => {
				secureStorage.removeItem.mockResolvedValue();

				await repository.removeSecureData('custom-key');

				expect(secureStorage.removeItem).toHaveBeenCalledWith('custom-key');
			});

			it('should throw error when removal fails', async () => {
				secureStorage.removeItem.mockRejectedValue(new Error('Removal failed'));

				await expect(repository.removeSecureData('custom-key')).rejects.toThrow(
					'Failed to remove secure data for key custom-key: Error: Removal failed',
				);
			});
		});
	});

	describe('clearAllUserData', () => {
		it('should clear all user-related data', async () => {
			secureStorage.removeItem.mockResolvedValue();

			await repository.clearAllUserData();

			expect(secureStorage.removeItem).toHaveBeenCalledWith(StorageKeys.AUTH_TOKEN);
			expect(secureStorage.removeItem).toHaveBeenCalledWith(StorageKeys.REFRESH_TOKEN);
			expect(secureStorage.removeItem).toHaveBeenCalledWith('auth_expires_at');
			expect(secureStorage.removeItem).toHaveBeenCalledWith(StorageKeys.USER_PREFERENCES);
			expect(secureStorage.removeItem).toHaveBeenCalledWith(StorageKeys.BIOMETRIC_ENABLED);
			expect(secureStorage.removeItem).toHaveBeenCalledWith(StorageKeys.LAST_LOGIN);
		});

		it('should throw error when clearing fails', async () => {
			secureStorage.removeItem.mockRejectedValue(new Error('Clear failed'));

			await expect(repository.clearAllUserData()).rejects.toThrow(
				'Failed to clear all user data: Error: Clear failed',
			);
		});
	});
});
